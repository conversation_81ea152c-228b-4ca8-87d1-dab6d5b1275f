#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务510气体追踪数据为空问题诊断脚本

根据用户提供的信息：
- 任务510没有追踪数据，导致跳过可视化数据处理
- gasAnalysisSummary和finalTrackedLeakagePoints等字段为空
- gasLeakageTrackingData字段为null

本脚本将诊断predict-service中可能导致数据为空的原因
"""

import sys
import os
import logging
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.managers.config_manager import ConfigManager
from core.processors.video_processor import VideoProcessor
from services.video_service import VideoService

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_config_manager():
    """测试ConfigManager配置读取"""
    print("\n=== 测试ConfigManager配置读取 ===")
    try:
        config = ConfigManager()
        
        # 测试气体追踪相关配置
        gas_model_path = config.get_config_value('gas_tracking', 'model_path', 'models/gas_detection.pt')
        gas_confidence = config.get_config_value('gas_tracking', 'confidence_threshold', 0.5)
        gas_enabled = config.get_config_value('gas_tracking', 'enabled', True)
        
        print(f"✓ 气体模型路径: {gas_model_path}")
        print(f"✓ 气体置信度阈值: {gas_confidence}")
        print(f"✓ 气体追踪启用: {gas_enabled}")
        
        # 检查模型文件是否存在
        if os.path.exists(gas_model_path):
            print(f"✓ 气体模型文件存在: {gas_model_path}")
        else:
            print(f"✗ 气体模型文件不存在: {gas_model_path}")
            
        return True
        
    except Exception as e:
        print(f"✗ ConfigManager测试失败: {e}")
        return False

def test_video_processor_initialization():
    """测试VideoProcessor初始化"""
    print("\n=== 测试VideoProcessor初始化 ===")
    try:
        config = ConfigManager()
        
        # 创建VideoProcessor实例
        video_processor = VideoProcessor(
            config=config,
            ocr_processor=None,
            gas_leakage_mode=True
        )
        
        print(f"✓ VideoProcessor创建成功")
        print(f"✓ 气体泄漏模式: {video_processor.gas_leakage_mode}")
        print(f"✓ 设备: {video_processor.device}")
        print(f"✓ gas_tracking_results初始状态: {len(video_processor.gas_tracking_results)}")
        
        # 测试模型初始化
        try:
            # 使用配置中的模型路径
            model_path = config.get_config_value('gas_tracking', 'model_path', './models/best-mini.pt')
            video_processor.initialize_processing(
                video_path="test_video.mp4",  # 模拟视频路径
                model_path=model_path
            )
            print(f"✓ 模型初始化成功")
            print(f"✓ 模型路径: {video_processor.model_path}")
            print(f"✓ 追踪器配置: {video_processor.tracker_config_path}")
            
            if video_processor.model is not None:
                print(f"✓ YOLO模型加载成功")
            else:
                print(f"✗ YOLO模型未加载")
                
        except Exception as e:
            print(f"✗ 模型初始化失败: {e}")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ VideoProcessor初始化失败: {e}")
        return False

def test_gas_tracking_data_flow():
    """测试气体追踪数据流"""
    print("\n=== 测试气体追踪数据流 ===")
    try:
        config = ConfigManager()
        video_processor = VideoProcessor(
            config=config,
            ocr_processor=None,
            gas_leakage_mode=True
        )
        
        # 初始化处理
        model_path = config.get_config_value('gas_tracking', 'model_path', './models/best-mini.pt')
        video_processor.initialize_processing(
            video_path="test_video.mp4",  # 模拟视频路径
            model_path=model_path
        )
        
        # 创建模拟帧数据
        import numpy as np
        import cv2
        
        # 创建一个640x480的测试帧
        test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        test_frame[:] = (100, 100, 100)  # 灰色背景
        
        # 添加一些模拟的气体区域（白色矩形）
        cv2.rectangle(test_frame, (200, 150), (400, 300), (255, 255, 255), -1)
        
        print(f"✓ 创建测试帧: {test_frame.shape}")
        
        # 测试帧处理
        frame_gray = cv2.cvtColor(test_frame, cv2.COLOR_BGR2GRAY)
        
        frame_result = video_processor.process_frame(
            frame=test_frame,
            model=None,  # 使用内置模型
            confidence_threshold=0.3,
            prev_frame_gray=None,
            frame_gray=frame_gray,
            scale=1.0,
            leak_area=None,
            fps=30.0,
            task_id=510,
            label_service=None
        )
        
        print(f"✓ 帧处理完成")
        print(f"✓ frame_result类型: {type(frame_result)}")
        
        if isinstance(frame_result, dict):
            print(f"✓ frame_result包含的键: {list(frame_result.keys())}")
            
            # 检查气体模式
            gas_mode = frame_result.get('gas_mode', False)
            print(f"✓ 气体模式: {gas_mode}")
            
            # 检查gas_tracking_results
            gas_tracking_results = frame_result.get('gas_tracking_results')
            print(f"✓ gas_tracking_results类型: {type(gas_tracking_results)}")
            
            if gas_tracking_results:
                print(f"✓ gas_tracking_results内容: {json.dumps(gas_tracking_results, indent=2, default=str)}")
            else:
                print(f"✗ gas_tracking_results为空或None")
                
            # 检查video_processor的gas_tracking_results列表
            print(f"✓ video_processor.gas_tracking_results长度: {len(video_processor.gas_tracking_results)}")
            
            if video_processor.gas_tracking_results:
                latest_result = video_processor.gas_tracking_results[-1]
                print(f"✓ 最新追踪结果: {json.dumps(latest_result, indent=2, default=str)}")
            else:
                print(f"✗ video_processor.gas_tracking_results为空")
                
        else:
            print(f"✗ frame_result不是字典类型: {frame_result}")
            
        return True
        
    except Exception as e:
        print(f"✗ 气体追踪数据流测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_video_service_integration():
    """测试VideoService集成"""
    print("\n=== 测试VideoService集成 ===")
    try:
        config = ConfigManager()
        
        # 创建VideoService实例（模拟app.py中的初始化）
        video_service = VideoService(
            config=config,
            label_service=None,  # 模拟
            ocr_processor=None   # 模拟
        )
        
        print(f"✓ VideoService创建成功")
        print(f"✓ video_processor已初始化，气体追踪默认启用")
        
        # 测试_update_task_progress方法的数据构建逻辑
        # 模拟frame_result数据
        mock_frame_result = {
            'gas_mode': True,
            'gas_tracking_results': {
                'frame_number': 100,
                'timestamp': 3.33,
                'detections': [
                    {
                        'bbox': [200, 150, 400, 300],
                        'confidence': 0.85,
                        'class_id': 0,
                        'track_id': 1
                    }
                ],
                'tracked_objects': [
                    {
                        'track_id': 1,
                        'class_name': 'gas_leak',
                        'confidence': 0.85,
                        'center': (300, 225),
                        'area': 30000
                    }
                ],
                'camera_motion': {
                    'confidence': 0.7,
                    'translation': (2.1, -1.5),
                    'rotation': 0.02
                },
                'dispersion_analysis': {
                    'active_leakage_count': 1,
                    'total_leakage_area': 30000,
                    'risk_level': 'medium'
                }
            }
        }
        
        print(f"✓ 模拟frame_result创建成功")
        
        # 测试gasLeakageTrackingData的生成
        gas_tracking_results = mock_frame_result.get('gas_tracking_results', {})
        if gas_tracking_results:
            gas_leakage_tracking_data = json.dumps(
                video_service._convert_numpy_to_json_serializable(gas_tracking_results)
            )
            print(f"✓ gasLeakageTrackingData生成成功")
            print(f"✓ 数据长度: {len(gas_leakage_tracking_data)} 字符")
            print(f"✓ 数据预览: {gas_leakage_tracking_data[:200]}...")
        else:
            print(f"✗ gas_tracking_results为空，无法生成gasLeakageTrackingData")
            
        return True
        
    except Exception as e:
        print(f"✗ VideoService集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_task_510_issue():
    """分析任务510问题的可能原因"""
    print("\n=== 任务510问题分析 ===")
    
    print("\n根据用户提供的数据分析:")
    print("- gasAnalysisSummary: 有数据但totalConfirmedLeaks=0")
    print("- finalTrackedLeakagePoints: 空列表[]")
    print("- gasLeakageTrackingData: null")
    print("- gasTrackingSummary: 空字典{}")
    
    print("\n可能的原因:")
    print("1. 视频中确实没有检测到气体泄漏")
    print("2. 气体检测模型置信度阈值过高")
    print("3. 气体追踪模型初始化失败")
    print("4. 帧处理过程中gas_tracking_results未正确生成")
    print("5. 数据传递过程中丢失")
    
    print("\n建议检查:")
    print("1. 检查气体检测模型文件是否存在")
    print("2. 降低置信度阈值进行测试")
    print("3. 检查视频帧是否包含可检测的气体特征")
    print("4. 查看详细的处理日志")

def main():
    """主函数"""
    print("任务510气体追踪数据为空问题诊断")
    print("=" * 50)
    
    # 执行各项测试
    tests = [
        ("ConfigManager配置读取", test_config_manager),
        ("VideoProcessor初始化", test_video_processor_initialization),
        ("气体追踪数据流", test_gas_tracking_data_flow),
        ("VideoService集成", test_video_service_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n✗ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果汇总
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    # 问题分析
    analyze_task_510_issue()
    
    print("\n诊断完成!")

if __name__ == "__main__":
    main()