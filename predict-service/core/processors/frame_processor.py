#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
帧处理器 | Frame Processor
=========================

视频帧级别的处理和分析引擎
Frame-level processing and analysis engine

主要功能 | Main Features:
- 单帧图像处理 | Single frame image processing
- 目标检测与识别 | Object detection and recognition
- 帧级特征提取 | Frame-level feature extraction
- GPU内存优化 | GPU memory optimization

作者 | Author: <PERSON><PERSON><PERSON><PERSON>
创建时间 | Created: 2024
最后修改 | Last Modified: 2025
"""

import sys
import traceback
import cv2
import numpy as np
from typing import Tuple, List, Any
import threading
import torch
import gc
import logging
from utils.cuda_utils import safe_cuda_empty_cache

logger = logging.getLogger(__name__)

# Mock类已移除 - 不再需要检测处理器转换逻辑

class FrameProcessor:
    def __init__(self, config=None):
        """初始化帧处理器，添加线程安全保护

        Args:
            config: 配置管理器，用于读取CUDA设置等参数
        """
        self._lock = threading.RLock() # 线程安全锁
        self._inference_count = 0
        self.config = config

        # 从配置中读取CUDA设置
        self._cuda_enabled = self._get_cuda_setting()

    def _get_cuda_setting(self):
        """从配置中获取CUDA设置

        Returns:
            bool: 是否启用CUDA
        """
        try:
            # 优先从配置对象中读取
            if self.config and hasattr(self.config, 'enable_cuda'):
                cuda_enabled = self.config.enable_cuda
                logger.info(f"从配置对象读取CUDA设置: {cuda_enabled}")
                return cuda_enabled

            # 从环境变量读取
            import os
            enable_cuda_env = os.environ.get('ENABLE_CUDA', 'true').lower() == 'true'
            cuda_visible_devices = os.environ.get('CUDA_VISIBLE_DEVICES', '')

            # 如果CUDA_VISIBLE_DEVICES设置为-1，则禁用CUDA
            if cuda_visible_devices == '-1':
                logger.info("CUDA_VISIBLE_DEVICES=-1，禁用CUDA")
                return False

            logger.info(f"从环境变量读取CUDA设置: ENABLE_CUDA={enable_cuda_env}")
            return enable_cuda_env

        except Exception as e:
            logger.warning(f"读取CUDA配置失败，使用默认值False: {e}")
            return False

    def process_frame(self, frame, model, confidence_threshold, prev_frame_gray,
                     frame_gray, scale, leak_area, fps, task_id=None, label_service=None):
        """处理单帧 - 添加线程安全和内存保护"""
        with self._lock: # 确保模型推理的线程安全
            try:

                logger.debug(f"模型类型检查通过: {type(model).__name__}")

                # 检查frame是否有效
                if frame is None:
                    logger.warning("输入帧为None，返回空结果")
                    return self._empty_result((480, 640, 3))

                if not hasattr(frame, 'shape') or len(frame.shape) < 2:
                    logger.error(f"输入帧格式无效，类型: {type(frame)}")
                    return self._empty_result((480, 640, 3))

                # GPU内存检查和管理 - 安全清理CUDA缓存
                # 使用配置中的CUDA设置来决定是否执行CUDA操作
                if (self._cuda_enabled and hasattr(torch.cuda, 'is_available') and torch.cuda.is_available()):
                    # 如果使用GPU，定期清理缓存
                    if hasattr(torch.cuda, 'empty_cache'):
                        try:
                            # 同步CUDA操作
                            torch.cuda.synchronize()
                            # 清理缓存（只有在CUDA启用时）
                            if self._cuda_enabled:
                                safe_cuda_empty_cache()
                        except RuntimeError as cuda_error:
                            if "misaligned address" in str(cuda_error):
                                logger.warning(f"CUDA内存对齐错误，尝试重置CUDA状态: {cuda_error}")
                                # 尝试重置CUDA状态
                                try:
                                    torch.cuda.reset_peak_memory_stats()
                                    if self._cuda_enabled:
                                        safe_cuda_empty_cache()
                                except Exception as reset_error:
                                    logger.error(f"CUDA状态重置失败: {reset_error}")
                            else:
                                raise cuda_error

                # 内存保护：确保输入数据连续性
                if not frame.flags['C_CONTIGUOUS']:
                    frame = np.ascontiguousarray(frame)

                # 安全的模型推理
                with torch.no_grad(): # 禁用梯度计算
                    try:
                        # 修复设备检查 - 正确处理torch.device对象
                        device = getattr(model, 'device', 'cpu')
                        if hasattr(device, 'type'):
                            # 如果是torch.device对象，获取其type属性
                            device_str = device.type
                        else:
                            # 如果是字符串，直接使用
                            device_str = str(device)

                        logger.debug(f"Model device: {device_str}")

                        # 准备输入数据
                        prepared_frame = self._prepare_frame_for_inference(frame)

                        # 推理前的模型检查
                        if not callable(model):
                            logger.error("模型在推理前变为不可用状态")
                            return self._empty_result(frame.shape)

                        # 根据配置选择推理模式
                        logger.debug(f"开始模型推理，模型类型: {type(model).__name__}，CUDA启用: {self._cuda_enabled}")

                        # 根据配置决定推理模式
                        if self._cuda_enabled:
                            # CUDA模式推理
                            results = self._cuda_inference(model, prepared_frame, confidence_threshold)
                        else:
                            # CPU模式推理
                            results = self._cpu_inference(model, prepared_frame, confidence_threshold)

                        # 立即处理结果，避免GPU内存累积
                        detection_count = 0
                        if results and len(results) > 0:
                            result = results[0]
                            if hasattr(result, 'boxes') and result.boxes is not None:
                                detection_count = len(result.boxes)

                        # 清理GPU内存 - 安全清理（只有在CUDA启用时）
                        if self._cuda_enabled and device_str.startswith('cuda'):
                            safe_cuda_empty_cache()

                        self._inference_count += 1
                        logger.debug(f"Frame processed successfully, detections: {detection_count}")

                        # 处理推理结果并返回完整的结果结构
                        if results and len(results) > 0:
                            return self._process_results(results, frame, prev_frame_gray, frame_gray,
                                                       scale, leak_area, fps, task_id, label_service)
                        else:
                            # 没有检测结果时返回空结果
                            return self._empty_result(frame.shape)

                    except Exception as inference_error:
                        logger.error(f"推理失败: {inference_error}")
                        
                        # 清理内存（只有在CUDA启用时）
                        if self._cuda_enabled:
                            safe_cuda_empty_cache()

                        return self._empty_result(frame.shape)

            except Exception as e:
                logger.error(f"帧处理出现严重错误: {e}")
                logger.error(f"异常类型: {type(e).__name__}")
                logger.error(f"详细调用栈:")

                # 获取完整的调用栈
                exc_type, exc_value, exc_traceback = sys.exc_info()
                stack_trace = traceback.format_exception(exc_type, exc_value, exc_traceback)
                for line in stack_trace:
                    logger.error(line.rstrip())

                # 记录调试信息
                logger.error(f"模型参数类型: {type(model)}, 值: {model}")
                logger.error(f"帧参数类型: {type(frame) if frame is not None else 'None'}")
                if frame is not None and hasattr(frame, 'shape'):
                    logger.error(f"帧形状: {frame.shape}")

                # 强制内存清理 - 安全清理（只有在CUDA启用时）
                try:
                    gc.collect()
                    if self._cuda_enabled:
                        safe_cuda_empty_cache()
                except Exception:
                    pass

                # 返回空结果而不是None，确保调用方不会出错
                return self._empty_result(frame.shape if frame is not None and hasattr(frame, 'shape') else (480, 640, 3))

    def _prepare_frame_for_inference(self, frame):
        """为推理准备帧数据"""
        try:
            # 确保图像格式正确
            if len(frame.shape) == 3 and frame.shape[2] == 3:
                # BGR图像，直接使用
                prepared_frame = frame
            elif len(frame.shape) == 2:
                # 灰度图像，转换为BGR
                prepared_frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)
            else:
                logger.warning(f"未知的图像格式: {frame.shape}")
                prepared_frame = frame

            # 确保数据类型正确
            if prepared_frame.dtype != np.uint8:
                prepared_frame = prepared_frame.astype(np.uint8)

            # 确保内存连续性
            if not prepared_frame.flags['C_CONTIGUOUS']:
                prepared_frame = np.ascontiguousarray(prepared_frame)

            return prepared_frame

        except Exception as e:
            logger.error(f"准备推理数据时出错: {e}")
            return frame # 返回原始帧作为后备

    def _process_results(self, results, frame, prev_frame_gray, frame_gray,
                        scale, leak_area, fps, task_id, label_service):
        """处理推理结果"""
        overlay = np.zeros_like(frame)
        optical_flow_result = np.zeros_like(frame)
        max_velocity = 0
        frame_has_detection = False
        max_confidence_value = 0
        label_info = None

        for r in results:
            if not r.masks:
                continue
            frame_has_detection = True
            masks = r.masks
            boxes = r.boxes
            masks_xy = masks.xy
            boxes_cls = boxes.cls.cpu().numpy().astype(int)
            confidences = boxes.conf.cpu().numpy()

            frame_max_confidence = confidences.max() if len(confidences) > 0 else 0
            max_confidence_value = max(max_confidence_value, frame_max_confidence)

            for i in range(len(masks_xy)):
                mask = masks_xy[i]
                cls = boxes_cls[i]
                confidence = confidences[i]

                # 如果提供了标签服务，则使用标签等级信息
                if label_service and task_id:
                    label_info = label_service.process_detection(task_id, str(cls), confidence)
                    poly_color = label_info['color']
                else:
                    label_info = None
                    poly_color = None

                self._process_mask(overlay, mask, cls, poly_color=poly_color)

                if prev_frame_gray is not None:
                    flow_result = self._compute_flow(prev_frame_gray, frame_gray, mask, fps, scale)
                    optical_flow_result += flow_result['image']
                    max_velocity = max(max_velocity, flow_result['velocity'])

        confidence_map = self._create_confidence_heatmap(frame, results)
        heatmap_frame = self._apply_confidence_overlay(confidence_map, frame.copy())
        confidence_result = cv2.addWeighted(frame, 0.5, heatmap_frame, 0.5, 0)
        detection_result = cv2.addWeighted(frame, 0.8, overlay, 0.2, 0)

        # Convert numpy types to Python native types for JSON serialization
        return {
            'has_detection': bool(frame_has_detection),
            'overlay': overlay,
            'optical_flow': optical_flow_result,
            'max_velocity': float(max_velocity),
            'max_confidence': float(max_confidence_value),
            'results': results,
            'confidence': confidence_result,
            'detection': detection_result,
            'labelInfo': label_info
        }

    def _empty_result(self, frame_shape=(480, 640, 3)):
        """返回空结果"""
        empty_frame = np.zeros(frame_shape, dtype=np.uint8)
        return {
            'has_detection': False,
            'overlay': empty_frame,
            'optical_flow': empty_frame,
            'max_velocity': 0.0,
            'max_confidence': 0.0,
            'results': [],
            'confidence': empty_frame,
            'detection': empty_frame,
            'labelInfo': None
        }

    def _cleanup_memory(self):
        """清理内存（只有在CUDA启用时才清理CUDA缓存）"""
        try:
            gc.collect()
            if self._cuda_enabled:
                safe_cuda_empty_cache()
        except Exception:
            pass

    def _process_mask(self, overlay, mask, cls, poly_color=None, poly_thickness=2):
        """处理遮罩"""
        if poly_color is None:
            poly_color = (0, 0, 255) # 默认红色

        for point in mask:
            cv2.circle(overlay, (int(point[0]), int(point[1])), 1,
                      (0, 255, 0) if cls == 0 else (0, 0, 255), -1)
        cv2.polylines(overlay, [np.array(mask, dtype=np.int32)],
                     isClosed=True, color=poly_color, thickness=poly_thickness)

    def _compute_flow(self, prev_gray, curr_gray, mask, fps, scale):
        """计算光流并添加方向箭头可视化 + 梯度增强泄漏源定位"""
        flow = cv2.calcOpticalFlowFarneback(prev_gray, curr_gray, None, 0.5, 3, 15, 3, 5, 1.2, 0)
        magnitude, angle = cv2.cartToPolar(flow[..., 0], flow[..., 1])

        # 修复：安全地处理mask数据，避免索引错误
        try:
            if isinstance(mask, (list, tuple)):
                mask_points = np.array(mask, dtype=np.int32)
            elif isinstance(mask, np.ndarray):
                mask_points = mask.astype(np.int32)
            else:
                logger.warning(f"未知的mask类型: {type(mask)}")
                mask_points = np.array([[0, 0], [1, 0], [1, 1], [0, 1]], dtype=np.int32)

            mask_img = np.zeros_like(prev_gray)
            cv2.fillPoly(mask_img, [mask_points], 1)
        except Exception as e:
            logger.error(f"创建mask失败: {e}")
            mask_img = np.zeros_like(prev_gray)

        masked_magnitude = magnitude * mask_img
        mean_magnitude = np.mean(masked_magnitude[masked_magnitude > 0]) if np.any(masked_magnitude > 0) else 0
        velocity = mean_magnitude * scale * fps # pixels/frame * meters/pixel * frames/second = meters/second

        # Convert grayscale to BGR for background
        background = cv2.cvtColor(prev_gray, cv2.COLOR_GRAY2BGR)

        # If significant motion is detected, show optical flow
        if mean_magnitude > 0.1: # Threshold can be adjusted
            # 创建增强的HSV光流可视化
            result = self._create_enhanced_hsv_flow_visualization(
                background, flow, magnitude, angle, mask_img
            )

            # 添加方向箭头提升可解释性
            result = self._draw_flow_arrows(result, flow, mask_img, magnitude, angle)

        else:
            # If no significant motion, return original frame
            result = background

        # 新增：梯度增强泄漏源定位分析
        # enhanced_result = self._apply_gradient_enhanced_analysis(
        #     prev_gray, curr_gray, mask_img, flow, magnitude, angle, result, velocity
        # )

        # 返回正确的字典格式
        return {
            'image': result,
            'velocity': velocity
        }

    def _draw_flow_arrows(self, image, flow, mask_img, magnitude, angle):
        """
        在光流图像上绘制方向箭头

        Args:
            image: 当前光流可视化图像
            flow: 光流向量场
            mask_img: 掩码图像
            magnitude: 光流幅度
            angle: 光流角度

        Returns:
            添加了方向箭头的图像
        """
        h, w = image.shape[:2]

        # 设置箭头采样间隔（增加箭头密度）
        step = 12 # 每12像素绘制一个箭头（原来20，现在更密集）
        arrow_scale = 25 # 箭头长度缩放因子（原来15，现在更大）
        min_magnitude_threshold = 0.3 # 最小幅度阈值，降低阈值显示更多箭头

        for y in range(step//2, h, step):
            for x in range(step//2, w, step):
                # 修复：添加边界检查，避免索引越界
                if y >= h or x >= w or y < 0 or x < 0:
                    continue

                # 检查是否在掩码区域内
                if mask_img[y, x] == 0:
                    continue

                # 获取当前点的光流信息
                mag = magnitude[y, x]
                ang = angle[y, x]

                # 只绘制显著运动的箭头
                if mag < min_magnitude_threshold:
                    continue

                # 计算箭头终点
                dx = flow[y, x, 0]
                dy = flow[y, x, 1]

                # 限制箭头长度
                arrow_length = min(mag * arrow_scale, step * 0.8)
                end_x = int(x + dx * arrow_length / mag) if mag > 0 else x
                end_y = int(y + dy * arrow_length / mag) if mag > 0 else y

                # 确保箭头终点在图像范围内
                end_x = max(0, min(w-1, end_x))
                end_y = max(0, min(h-1, end_y))

                # 根据幅度设置箭头颜色（绿色到红色渐变）
                if mag < 1.0:
                    color = (0, 255, 0) # 绿色 - 慢速运动
                elif mag < 3.0:
                    color = (0, 255, 255) # 黄色 - 中速运动
                else:
                    color = (0, 0, 255) # 红色 - 快速运动

                # 绘制箭头主线（增加线条粗细）
                cv2.arrowedLine(image, (x, y), (end_x, end_y), color, 3, tipLength=0.4)

                # 在箭头起点绘制小圆点（增大圆点）
                cv2.circle(image, (x, y), 3, color, -1)

        return image

    def _create_enhanced_hsv_flow_visualization(self, background, flow, magnitude, angle, mask_img):
        """
        创建增强的HSV光流可视化

        在保留现有特征的基础上，提供更丰富的HSV颜色空间可视化：
        - 多层次颜色映射
        - 自适应亮度调整
        - 增强的饱和度控制
        - 流向和强度的双重编码

        Args:
            background: 背景图像
            flow: 光流向量场
            magnitude: 光流幅度
            angle: 光流角度
            mask_img: 掩码图像

        Returns:
            增强的HSV光流可视化图像
        """
        h, w = background.shape[:2]

        # 1. 创建基础HSV图像
        hsv = np.zeros((h, w, 3), dtype=np.uint8)

        # 2. 色相通道：编码流动方向
        # 将角度从[-π, π]映射到[0, 179]
        # 修复：处理angle中的NaN值
        angle_safe = np.nan_to_num(angle, nan=0.0, posinf=0.0, neginf=0.0)
        hue_raw = (angle_safe + np.pi) * 179 / (2 * np.pi)
        hue_safe = np.nan_to_num(hue_raw, nan=0.0, posinf=179.0, neginf=0.0)
        hue = np.clip(hue_safe, 0, 179).astype(np.uint8)
        hsv[..., 0] = hue

        # 3. 饱和度通道：自适应饱和度，增强可视化效果
        # 3. 饱和度通道：自适应饱和度控制
        # 修复：在normalize之前处理NaN值
        magnitude_clean = np.nan_to_num(magnitude, nan=0.0, posinf=0.0, neginf=0.0)
        magnitude_normalized = cv2.normalize(magnitude_clean, None, 0, 1, cv2.NORM_MINMAX)
        saturation_raw = magnitude_normalized * 255 * 1.2
        saturation_safe = np.nan_to_num(saturation_raw, nan=0.0, posinf=255.0, neginf=0.0)
        saturation = np.clip(saturation_safe, 0, 255).astype(np.uint8)
        hsv[..., 1] = saturation

        # 4. 亮度通道：多层次亮度映射
        # 使用非线性映射增强细节可见性
        # 修复：处理NaN和无穷大值，避免RuntimeWarning
        magnitude_safe = np.nan_to_num(magnitude_normalized, nan=0.0, posinf=1.0, neginf=0.0)
        # 确保所有值都在有效范围内
        magnitude_safe = np.clip(magnitude_safe, 0.0, 1.0)
        magnitude_enhanced = np.power(magnitude_safe, 0.7) # 伽马校正
        brightness_base = 180 # 基础亮度
        brightness_range = 75 # 亮度变化范围
        brightness_raw = brightness_base + magnitude_enhanced * brightness_range
        brightness_safe = np.nan_to_num(brightness_raw, nan=brightness_base, posinf=255, neginf=50)
        brightness = np.clip(brightness_safe, 50, 255).astype(np.uint8)
        hsv[..., 2] = brightness

        # 5. 转换为BGR颜色空间
        flow_rgb = cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)

        # 6. 应用掩码
        masked_flow = flow_rgb * mask_img[..., np.newaxis]

        # 7. 创建多层次混合效果
        # 主要光流可视化
        alpha_main = 0.8
        result_main = cv2.addWeighted(background, 1.0, masked_flow, alpha_main, 0)

        # 8. 添加高强度区域的额外高亮
        # 修复：检查mask区域是否为空，避免索引错误
        mask_values = magnitude[mask_img > 0]
        if len(mask_values) > 0:
            high_intensity_threshold = np.percentile(mask_values, 85)
            high_intensity_mask = (magnitude > high_intensity_threshold).astype(np.uint8)
        else:
            high_intensity_mask = np.zeros_like(magnitude, dtype=np.uint8)

        if np.any(high_intensity_mask):
            # 创建高亮HSV图像
            hsv_highlight = hsv.copy()
            hsv_highlight[..., 1] = 255 # 最大饱和度
            hsv_highlight[..., 2] = 255 # 最大亮度
            flow_highlight = cv2.cvtColor(hsv_highlight, cv2.COLOR_HSV2BGR)

            # 应用高强度掩码
            highlight_mask = high_intensity_mask * mask_img
            masked_highlight = flow_highlight * highlight_mask[..., np.newaxis]

            # 混合高亮效果
            alpha_highlight = 0.3
            result_main = cv2.addWeighted(result_main, 1.0, masked_highlight, alpha_highlight, 0)

        # 9. 添加流动边界增强
        # 使用形态学操作检测流动边界
        magnitude_binary = (magnitude_normalized > 0.1).astype(np.uint8) * mask_img
        kernel = np.ones((3, 3), np.uint8)
        edges = cv2.morphologyEx(magnitude_binary, cv2.MORPH_GRADIENT, kernel)

        if np.any(edges):
            # 在边界处添加白色轮廓
            edge_color = (255, 255, 255)
            result_main[edges > 0] = edge_color

        return result_main

    def _apply_gradient_enhanced_analysis(self, prev_gray, curr_gray, mask_img, flow, magnitude, angle, flow_image, velocity):
        """
        应用梯度增强泄漏源定位分析

        在现有光流分析基础上，集成梯度分析和泄漏源定位功能。
        这是对原有光流分析的增强，不影响现有功能。

        Args:
            prev_gray: 前一帧灰度图
            curr_gray: 当前帧灰度图
            mask_img: 气体检测mask
            flow: 光流向量场
            magnitude: 光流幅度
            angle: 光流角度
            flow_image: 原始光流可视化图像
            velocity: 计算得到的速度值

        Returns:
            增强后的分析结果字典
        """
        try:
            # 导入改进版梯度增强模块
            from core.processors.improved_gradient_localizer import ImprovedGradientLeakageLocalizer

            # 准备输入数据
            prev_frame_bgr = cv2.cvtColor(prev_gray, cv2.COLOR_GRAY2BGR)
            curr_frame_bgr = cv2.cvtColor(curr_gray, cv2.COLOR_GRAY2BGR)

            # 创建YOLO mask格式（255为mask区域）
            yolo_mask = (mask_img * 255).astype(np.uint8)

            # 创建改进版梯度定位器实例
            if not hasattr(self, '_improved_localizer'):
                self._improved_localizer = ImprovedGradientLeakageLocalizer(debug_mode=False)

            # 执行改进版梯度增强分析
            # 注意：这里使用flow作为光流向量场，而不是velocity（velocity是标量）
            enhanced_result = self._improved_localizer.analyze_gradient_and_flow(
                prev_frame_bgr,
                curr_frame_bgr,
                yolo_mask,
                flow,  # 光流向量场
                magnitude,  # 光流幅度
                angle  # 光流角度
            )

            # 合并原有光流结果
            enhanced_result.update({
                'image': enhanced_result.get('overlay_frame', flow_image),
                'velocity': velocity
            })

            # 添加泄漏源点信息到返回结果中
            leak_sources = enhanced_result.get('leak_source_points', [])
            if leak_sources:
                logger.info(f"检测到 {len(leak_sources)} 个潜在气体泄漏源点（改进版）")
                for i, source in enumerate(leak_sources):
                    tracker_id = source.get('tracker_id', -1)
                    stability = source.get('stability_score', 0.0)
                    if tracker_id >= 0:
                        logger.debug(f"泄漏源 {i+1}: 位置({source['x']}, {source['y']}), 置信度: {source['confidence']:.3f}, 跟踪器ID: {tracker_id}, 稳定性: {stability:.3f}")
                    else:
                        logger.debug(f"泄漏源 {i+1}: 位置({source['x']}, {source['y']}), 置信度: {source['confidence']:.3f}, 新检测")
                        
                # 添加跟踪稳定性报告
                stability_report = enhanced_result.get('tracking_stability', {})
                if stability_report:
                    avg_stability = stability_report.get('average_stability', 0.0)
                    active_trackers = stability_report.get('active_trackers', 0)
                    logger.info(f"跟踪稳定性: 平均稳定性 {avg_stability:.3f}, 活跃跟踪器 {active_trackers}")

            return enhanced_result

        except ImportError as e:
            logger.warning(f"梯度增强模块导入失败，使用标准光流分析: {e}")
            return {
                'image': flow_image,
                'velocity': velocity,
                'leak_source_points': [],
                'enhanced_analysis_available': False
            }
        except Exception as e:
            logger.error(f"梯度增强分析失败，回退到标准光流分析: {e}")

            exc_type, exc_value, exc_traceback = sys.exc_info()
            stack_trace = traceback.format_exception(exc_type, exc_value, exc_traceback)
            for line in stack_trace:
                logger.error(line.rstrip())

            logger.error(f"梯度增强分析失败，回退到标准光流分析: {e}")
            return {
                'image': flow_image,
                'velocity': velocity,
                'leak_source_points': [],
                'enhanced_analysis_error': str(e)
            }

    def _create_confidence_heatmap(self, frame, results):
        """创建置信度热力图，替代马赛克效果"""
        confidence_map = np.zeros((frame.shape[0], frame.shape[1]), dtype=np.float32)

        for r in results:
            # 处理分割结果（有masks的情况）
            if r.masks is not None:
                for mask, conf in zip(r.masks.xy, r.boxes.conf):
                    polygon = np.array(mask, dtype=np.int32)
                    # 计算多边形的重心
                    M = cv2.moments(polygon)
                    if M["m00"] != 0:
                        cX = int(M["m10"] / M["m00"])
                        cY = int(M["m01"] / M["m00"])
                    else:
                        continue

                    # 创建平滑的高斯分布置信度图
                    mask_img = np.zeros_like(confidence_map)
                    cv2.fillPoly(mask_img, [polygon], 1)

                    # 使用高斯模糊创建平滑的置信度分布
                    gaussian_confidence = mask_img * conf.cpu().numpy()
                    gaussian_confidence = cv2.GaussianBlur(gaussian_confidence, (21, 21), 0)

                    confidence_map = np.maximum(confidence_map, gaussian_confidence)

            # 处理检测结果（只有检测框的情况）
            elif hasattr(r, 'boxes') and r.boxes is not None:
                try:
                    boxes = r.boxes.xyxy.cpu().numpy()
                    confidences = r.boxes.conf.cpu().numpy()

                    for i in range(len(boxes)):
                        box = boxes[i]
                        conf = confidences[i]

                        # 获取检测框坐标
                        x1, y1, x2, y2 = map(int, box)

                        # 确保坐标在图像范围内
                        x1 = max(0, min(x1, frame.shape[1]-1))
                        y1 = max(0, min(y1, frame.shape[0]-1))
                        x2 = max(0, min(x2, frame.shape[1]-1))
                        y2 = max(0, min(y2, frame.shape[0]-1))

                        if x2 > x1 and y2 > y1:
                            # 创建椭圆形的置信度分布
                            center_x = (x1 + x2) // 2
                            center_y = (y1 + y2) // 2
                            width = x2 - x1
                            height = y2 - y1

                            # 创建椭圆掩码
                            ellipse_mask = np.zeros_like(confidence_map)
                            cv2.ellipse(ellipse_mask, (center_x, center_y),
                                      (width//2, height//2), 0, 0, 360, conf, -1)

                            # 应用高斯模糊创建平滑过渡
                            kernel_size = min(31, max(5, min(width, height) // 3))
                            if kernel_size % 2 == 0:
                                kernel_size += 1
                            ellipse_mask = cv2.GaussianBlur(ellipse_mask, (kernel_size, kernel_size), 0)

                            confidence_map = np.maximum(confidence_map, ellipse_mask)

                except Exception as e:
                    logger.debug(f"处理检测框时出错: {e}")
                    continue

        return confidence_map

    def _apply_confidence_overlay(self, confidence_map, frame, alpha=0.6):
        """应用置信度热力图叠加，使用5x5像素块统一着色，浅绿色基调"""
        # 创建5x5像素块的置信度图（25个像素）
        block_confidence_map = self._create_block_confidence_map(confidence_map, block_size=5)

        # 使用浅绿色基调创建颜色映射
        confidence_colormap = self._create_green_confidence_colormap(block_confidence_map)

        # 创建掩码，只在有置信度的区域应用颜色
        mask = block_confidence_map > 0.1 # 设置阈值避免噪声

        # 创建结果图像
        result = frame.copy()

        # 在有置信度的区域进行加权混合
        if np.any(mask):
            # 使用掩码进行选择性混合
            mask_3d = np.stack([mask, mask, mask], axis=-1)
            result = np.where(mask_3d,
                            cv2.addWeighted(frame, 1-alpha, confidence_colormap, alpha, 0),
                            frame)

            # 添加置信度等高线以增强可视化效果
            confidence_normalized = (block_confidence_map * 255).astype(np.uint8)
            contours, _ = cv2.findContours(confidence_normalized, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            cv2.drawContours(result, contours, -1, (255, 255, 255), 1)

        return result

    def _create_block_confidence_map(self, confidence_map, block_size=5):
        """
        创建基于像素块的置信度图，每个块内的像素使用平均置信度

        Args:
            confidence_map: 原始置信度图
            block_size: 像素块大小（默认5x5，即25个像素）

        Returns:
            块化的置信度图
        """
        height, width = confidence_map.shape
        block_confidence_map = np.zeros_like(confidence_map)

        # 遍历每个块
        for y in range(0, height, block_size):
            for x in range(0, width, block_size):
                # 确定块的边界
                y_end = min(y + block_size, height)
                x_end = min(x + block_size, width)

                # 提取当前块
                block = confidence_map[y:y_end, x:x_end]

                # 计算块内非零像素的平均置信度
                non_zero_mask = block > 0
                if np.any(non_zero_mask):
                    avg_confidence = np.mean(block[non_zero_mask])
                    # 将平均置信度应用到整个块
                    block_confidence_map[y:y_end, x:x_end] = avg_confidence

        return block_confidence_map

    def _create_green_confidence_colormap(self, confidence_map):
        """
        创建浅绿色基调的置信度颜色映射

        Args:
            confidence_map: 置信度图

        Returns:
            浅绿色基调的彩色图像
        """
        # 归一化置信度图到0-1
        confidence_normalized = confidence_map.copy()
        max_conf = np.max(confidence_normalized)
        if max_conf > 0:
            confidence_normalized = confidence_normalized / max_conf

        # 创建浅绿色渐变
        # 使用HSV颜色空间，色相固定在绿色范围
        height, width = confidence_map.shape
        hsv_image = np.zeros((height, width, 3), dtype=np.float32)

        # 只在有置信度的区域计算颜色
        mask = confidence_map > 0

        if np.any(mask):
            # 色相：绿色范围 (OpenCV中绿色约为60度，转换后为30)
            hsv_image[mask, 0] = 60 # 绿色色相 (OpenCV范围0-179)

            # 饱和度：调整为更浅的绿色，降低饱和度范围
            # 基础饱和度为40 (0.16*255)，最高为120 (0.47*255) - 更浅的绿色
            hsv_image[mask, 1] = 40 + confidence_normalized[mask] * 80

            # 亮度：提高整体亮度，使颜色更浅
            # 基础亮度为240 (0.94*255)，最低为180 (0.71*255) - 更亮更浅
            hsv_image[mask, 2] = 240 - confidence_normalized[mask] * 60

        # 转换为BGR颜色空间
        hsv_image = hsv_image.astype(np.uint8)
        bgr_image = cv2.cvtColor(hsv_image, cv2.COLOR_HSV2BGR)

        return bgr_image

    def _create_confidence_legend(self, frame, confidence_map):
        """在图像上添加置信度图例，使用浅绿色渐变"""
        if confidence_map is None or not np.any(confidence_map > 0):
            return frame

        # 获取置信度范围
        min_conf = np.min(confidence_map[confidence_map > 0])
        max_conf = np.max(confidence_map)

        # 在图像右上角绘制图例
        legend_width = 20
        legend_height = 150
        legend_x = frame.shape[1] - legend_width - 10
        legend_y = 10

        # 创建浅绿色渐变条
        gradient = np.linspace(1, 0, legend_height).reshape(-1, 1)
        gradient = np.repeat(gradient, legend_width, axis=1)

        # 使用浅绿色基调创建图例
        gradient_colored = self._create_green_confidence_colormap(gradient)

        # 将图例叠加到图像上
        frame[legend_y:legend_y+legend_height, legend_x:legend_x+legend_width] = gradient_colored

        # 添加文字标签
        cv2.putText(frame, f'{max_conf:.2f}', (legend_x-40, legend_y+15),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        cv2.putText(frame, f'{min_conf:.2f}', (legend_x-40, legend_y+legend_height-5),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        cv2.putText(frame, 'Confidence', (legend_x-60, legend_y-10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

        return frame

    def _cuda_inference(self, model, frame, confidence_threshold):
        """
        CUDA模式推理

        Args:
            model: YOLO模型实例
            frame: 输入帧
            confidence_threshold: 置信度阈值

        Returns:
            推理结果或空列表
        """
        try:
            logger.debug("执行CUDA模式推理")

            # 检查是否为 VideoProcessor 实例（气体泄漏模式）
            if hasattr(model, 'gas_leakage_mode') and model.gas_leakage_mode:
                logger.debug("使用VideoProcessor气体泄漏模式进行推理")
                # 调用 VideoProcessor 的气体泄漏处理逻辑
                gas_result = model._process_gas_leakage_frame(frame, confidence_threshold)
                # 将气体泄漏结果转换为标准格式
                results = self._convert_gas_result_to_yolo_format(gas_result, frame)
            else:
                logger.debug("使用YOLO模型进行CUDA推理")
                results = model(frame, conf=confidence_threshold, verbose=False)

            logger.debug(f"CUDA推理完成，检测到 {len(results) if results else 0} 个结果")
            return results

        except Exception as e:
            logger.error(f"CUDA推理失败: {e}")
            return []

    def _cpu_inference(self, model, frame, confidence_threshold):
        """
        CPU模式推理

        Args:
            model: YOLO模型实例
            frame: 输入帧
            confidence_threshold: 置信度阈值

        Returns:
            推理结果或空列表
        """
        try:
            logger.debug("执行CPU模式推理")

            # 确保模型在CPU上
            if hasattr(model, 'to'):
                model.to('cpu')
                logger.debug("模型已移动到CPU")

            # 确保输入数据在CPU上
            if isinstance(frame, torch.Tensor):
                frame = frame.cpu()
                logger.debug("输入数据已移动到CPU")

            # 检查是否为 VideoProcessor 实例（气体泄漏模式）
            if hasattr(model, 'gas_leakage_mode') and model.gas_leakage_mode:
                logger.debug("使用VideoProcessor气体泄漏模式进行推理")
                # 调用 VideoProcessor 的气体泄漏处理逻辑
                gas_result = model._process_gas_leakage_frame(frame, confidence_threshold)
                # 将气体泄漏结果转换为标准格式
                results = self._convert_gas_result_to_yolo_format(gas_result, frame)
            else:
                logger.debug("使用YOLO模型进行CPU推理")
                results = model(frame, conf=confidence_threshold, verbose=False)

            logger.debug(f"CPU推理完成，检测到 {len(results) if results else 0} 个结果")
            return results

        except Exception as e:
            logger.error(f"CPU推理失败: {e}")
            return []

    def _convert_gas_result_to_yolo_format(self, gas_result, frame):
        """将气体泄漏结果转换为标准的 YOLO 格式"""
        try:
            if gas_result is None:
                return []

            # 如果气体泄漏结果已经是 YOLO 格式，直接返回
            if hasattr(gas_result, '__iter__') and len(gas_result) > 0:
                first_result = gas_result[0] if isinstance(gas_result, list) else gas_result
                if hasattr(first_result, 'boxes') or hasattr(first_result, 'masks'):
                    return gas_result

            # 如果是其他格式，创建空的 YOLO 结果
            logger.debug("气体泄漏结果格式不匹配，返回空结果")
            return []

        except Exception as e:
            logger.error(f"转换气体泄漏结果时出错: {e}")
            return []

    # _convert_detection_result_to_yolo_format方法已移除 - 不再需要检测处理器转换逻辑