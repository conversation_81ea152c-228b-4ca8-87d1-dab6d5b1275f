#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
光谱数据处理器 | Spectral Data Processor
======================================

光谱数据的分析和气体成分识别
Spectral data analysis and gas composition identification

主要功能 | Main Features:
- 光谱数据预处理 | Spectral data preprocessing
- FFT频域分析 | FFT frequency domain analysis
- 气体成分分析 | Gas composition analysis
- 峰值识别与分配 | Peak identification and assignment
- 质量指标计算 | Quality metrics calculation

作者 | Author: <PERSON><PERSON>kovasky
创建时间 | Created: 2024
最后修改 | Last Modified: 2025
"""

import numpy as np
from scipy.fft import fft, ifft
from scipy.signal import savgol_filter
from typing import List, Dict
import io
from dataclasses import dataclass

# TODO: Create proper models module
# from app.models import (
# SpectralAnalysis, SpectralPoint, GasComposition,
# SpectralMetadata, QualityMetrics
# )

@dataclass
class SpectralPoint:
    wavelength: float
    intensity: float
    absorbance: float
    peak_assignments: Dict[str, float]

@dataclass
class GasComposition:
    components: Dict[str, float]
    total_concentration: float
    major_components: List[str]
    trace_components: List[str]

@dataclass
class SpectralMetadata:
    instrument_id: str
    calibration_date: str
    resolution_wavenumber: float
    detector_type: str
    instrument_settings: Dict[str, str]

@dataclass
class QualityMetrics:
    signal_to_noise: float
    baseline_stability: float
    spectral_resolution: float
    detection_limit: float
    interference_factors: Dict[str, float]

@dataclass
class SpectralAnalysis:
    spectral_data: List[SpectralPoint]
    gas_composition: GasComposition
    metadata: SpectralMetadata
    quality_metrics: QualityMetrics

class SpectralProcessor:
    def process(self, spectral_data: bytes) -> np.ndarray:
        # 读取光谱数据
        data = np.genfromtxt(io.BytesIO(spectral_data), delimiter=',')

        # 提取强度数据
        intensities = data[:, 1]

        # 应用 Savitzky-Golay 滤波
        smoothed = savgol_filter(intensities, window_length=15, polyorder=3)

        # 应用 FFT 降噪
        return self._apply_fft(smoothed)

    def analyze(self, processed_data: np.ndarray) -> SpectralAnalysis:
        # 生成波长数组
        wavelengths = np.linspace(400, 2500, len(processed_data))

        # 计算吸光度
        absorbance = -np.log10(processed_data / np.max(processed_data))

        # 创建光谱点列表
        spectral_points = []
        for i in range(len(wavelengths)):
            point = SpectralPoint(
                wavelength=float(wavelengths[i]),
                intensity=float(processed_data[i]),
                absorbance=float(absorbance[i]),
                peak_assignments=self._assign_peaks(wavelengths[i])
            )
            spectral_points.append(point)

        # 分析气体组成
        gas_composition = self._analyze_composition(processed_data, wavelengths)

        # 创建元数据
        metadata = SpectralMetadata(
            instrument_id="SPEC001",
            calibration_date="2025-04-23",
            resolution_wavenumber=0.5,
            detector_type="FTIR",
            instrument_settings={
                "scan_speed": "2 scans/s",
                "resolution": "4 cm-1"
            }
        )

        # 计算质量指标
        quality_metrics = self._calculate_quality_metrics(processed_data)

        return SpectralAnalysis(
            spectral_data=spectral_points,
            gas_composition=gas_composition,
            metadata=metadata,
            quality_metrics=quality_metrics
        )

    def _apply_fft(self, data: np.ndarray) -> np.ndarray:
        # 应用 FFT
        fft_data = fft(data)

        # 设置截止频率
        cutoff = len(data) // 4
        fft_data[cutoff:-cutoff] = 0

        # 应用逆 FFT
        filtered_data = np.real(ifft(fft_data))

        return filtered_data

    def _assign_peaks(self, wavelength: float) -> Dict[str, float]:
        # 这里实现峰值分配逻辑
        # 这是一个简化的示例
        peaks = {}
        if 3000 <= wavelength <= 3100:
            peaks["CH4"] = 0.8
        elif 2850 <= wavelength <= 2950:
            peaks["C3H8"] = 0.6
        return peaks

    def _analyze_composition(self, data: np.ndarray, wavelengths: np.ndarray) -> GasComposition:
        # 这里实现气体组成分析逻辑
        # 这是一个简化的示例
        return GasComposition(
            components={
                "METHANE": 85.0,
                "ETHANE": 10.0,
                "PROPANE": 5.0
            },
            total_concentration=100.0,
            major_components=["METHANE", "ETHANE"],
            trace_components=["PROPANE"]
        )

    def _calculate_quality_metrics(self, data: np.ndarray) -> QualityMetrics:
        return QualityMetrics(
            signal_to_noise=35.0,
            baseline_stability=0.99,
            spectral_resolution=0.5,
            detection_limit=1.0,
            interference_factors={
                "water_vapor": 0.1,
                "co2": 0.05
            }
        )
