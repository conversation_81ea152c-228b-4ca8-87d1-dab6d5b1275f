#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
任务管理器 | Task Manager
========================

视频处理任务的生命周期管理
Lifecycle management for video processing tasks

主要功能 | Main Features:
- 任务创建与跟踪 | Task creation and tracking
- 进度状态管理 | Progress status management
- 多用户任务隔离 | Multi-user task isolation
- 自动清理机制 | Automatic cleanup mechanism

作者 | Author: <PERSON><PERSON><PERSON><PERSON>
创建时间 | Created: 2024
最后修改 | Last Modified: 2025
"""

import threading
import time
from typing import Dict, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class TaskProgress:
    """任务进度数据类"""
    video_id: str
    task_id: str
    user_id: str
    progress: int = 0
    frame_index: int = 0
    total_frames: int = 0
    fps: float = 0
    seconds: float = 0
    threshold_value: float = 0
    max_conf: float = 0
    # Volume calculation removed as per Issue #79
    # cumulative_leakage: float = 0
    velocity: float = 0
    pic_url: str = ""
    recog_date: Optional[str] = None
    recog_lat: Optional[str] = None
    recog_lon: Optional[str] = None
    status: str = "pending" # pending, processing, completed, failed
    error_message: Optional[str] = None
    last_update_time: float = 0

    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            'videoId': self.video_id,
            'taskId': self.task_id,
            'userId': self.user_id,
            'progress': self.progress,
            'frameIndex': self.frame_index,
            'totalFrames': self.total_frames,
            'fps': self.fps,
            'seconds': self.seconds,
            'thresholdValue': self.threshold_value,
            'maxConf': self.max_conf,
            # Volume calculation removed as per Issue #79
            # 'cumulativeLeakage': 0.0,
            'velocity': self.velocity,
            'picUrl': self.pic_url,
            'recogDate': self.recog_date,
            'recogLat': self.recog_lat,
            'recogLon': self.recog_lon,
            'status': self.status,
            'errorMessage': self.error_message,
            'lastUpdateTime': self.last_update_time
        }

class TaskManager:
    """任务管理器单例类"""
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(TaskManager, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self._tasks: Dict[str, TaskProgress] = {}
            self._tasks_lock = threading.Lock()
            self._cleanup_thread = threading.Thread(target=self._cleanup_old_tasks, daemon=True)
            self._cleanup_thread.start()
            self._initialized = True

    def create_task(self, video_id: str, task_id: str, user_id: str, total_frames: int, fps: float) -> TaskProgress:
        """创建新任务"""
        task = TaskProgress(
            video_id=video_id,
            task_id=task_id,
            user_id=user_id,
            total_frames=total_frames,
            fps=fps,
            last_update_time=time.time()
        )

        with self._tasks_lock:
            self._tasks[task_id] = task

        logger.info(f"Created task {task_id} for video {video_id}")
        return task

    def update_task_progress(self, task_id: str, **kwargs) -> None:
        """更新任务进度"""
        with self._tasks_lock:
            if task_id not in self._tasks:
                logger.warning(f"Task {task_id} not found")
                return

            task = self._tasks[task_id]
            for key, value in kwargs.items():
                if hasattr(task, key):
                    setattr(task, key, value)

            task.last_update_time = time.time()
            task.status = "processing"

    def get_task_progress(self, task_id: str) -> Optional[dict]:
        """获取任务进度"""
        with self._tasks_lock:
            task = self._tasks.get(task_id)
            return task.to_dict() if task else None

    def get_user_tasks(self, user_id: str) -> list:
        """获取用户的所有任务"""
        with self._tasks_lock:
            return [task.to_dict() for task in self._tasks.values() if task.user_id == user_id]

    def get_all_tasks(self) -> list:
        """获取所有任务"""
        with self._tasks_lock:
            return [task.to_dict() for task in self._tasks.values()]

    def mark_task_completed(self, task_id: str) -> None:
        """标记任务完成"""
        self.update_task_progress(task_id, status="completed", progress=100)
        logger.info(f"Task {task_id} marked as completed")

    def mark_task_failed(self, task_id: str, error_message: str = "Unknown error") -> None:
        """标记任务失败"""
        self.update_task_progress(task_id, status="failed", error_message=error_message)
        logger.error(f"Task {task_id} marked as failed: {error_message}")

    def remove_task(self, task_id: str) -> None:
        """移除任务"""
        with self._tasks_lock:
            removed_task = self._tasks.pop(task_id, None)
            if removed_task:
                logger.info(f"Removed task {task_id}")

    def get_task_count(self) -> dict:
        """获取任务统计信息"""
        with self._tasks_lock:
            status_count = {}
            for task in self._tasks.values():
                status = task.status
                status_count[status] = status_count.get(status, 0) + 1
            return {
                'total': len(self._tasks),
                'by_status': status_count
            }

    def _cleanup_old_tasks(self) -> None:
        """清理旧任务的后台线程"""
        cleanup_interval = 3600 # 1小时检查一次
        max_age = 24 * 3600 # 24小时后清理

        while True:
            try:
                current_time = time.time()
                with self._tasks_lock:
                    task_ids_to_remove = [
                        task_id for task_id, task in self._tasks.items()
                        if (current_time - task.last_update_time > max_age)
                        and task.status in ["completed", "failed"]
                    ]

                    for task_id in task_ids_to_remove:
                        self._tasks.pop(task_id, None)
                        logger.info(f"Cleaned up old task: {task_id}")

                    if task_ids_to_remove:
                        logger.info(f"Cleaned up {len(task_ids_to_remove)} old tasks")

                time.sleep(cleanup_interval)

            except Exception as e:
                logger.error(f"Error in task cleanup thread: {e}", exc_info=True)
                time.sleep(cleanup_interval)