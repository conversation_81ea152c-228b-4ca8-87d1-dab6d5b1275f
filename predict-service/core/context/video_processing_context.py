#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
视频处理上下文类 | Video Processing Context
==========================================

封装视频处理过程中的所有状态变量，避免多参数传递
Encapsulates all state variables during video processing to avoid multi-parameter passing

主要功能 | Main Features:
- 视频处理状态管理 | Video processing state management
- 进度跟踪与报告 | Progress tracking and reporting
- 帧处理计数器 | Frame processing counter
- OCR数据缓存 | OCR data caching
- 性能指标计算 | Performance metrics calculation

作者 | Author: <PERSON><PERSON><PERSON><PERSON>
创建时间 | Created: 2024
最后修改 | Last Modified: 2025
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any
import time
import numpy as np


@dataclass
class VideoProcessingContext:
    """视频处理上下文，封装所有状态变量"""

    # ========== 任务基本信息 ==========
    task_id: str
    video_id: str
    user_id: str
    confidence_threshold: float

    # ========== 视频元数据 ==========
    total_frames: int = 0
    fps: float = 0.0
    duration_seconds: float = 0.0
    frame_width: int = 0
    frame_height: int = 0

    # ========== 帧处理状态 ==========
    frame_counter: int = 0
    prev_frame_gray: Optional[np.ndarray] = None
    scale: float = 1.0 # 每个像素代表的实际距离（米）

    # ========== OCR 相关状态 ==========
    first_ocr_attempt_success: Optional[bool] = None
    first_ocr_data: Optional[Dict[str, Any]] = None
    recog_date: str = ""
    recog_lat: str = ""
    recog_lon: str = ""

    # ========== 进度报告状态 ==========
    progress_interval: int = 20 # 每N帧报告一次进度
    last_progress_time: float = field(default_factory=time.time)
    start_time: float = field(default_factory=time.time)

    # ========== 路径配置 ==========
    temp_frames_dir: Optional[str] = None
    output_dir: str = ""

    # ========== 配置选项 ==========
    save_no_detection_frames: bool = True # 是否保存无检测帧
    enable_progress_logging: bool = True # 是否启用进度日志
    mq_update_interval: int = 50 # MQ更新间隔（帧）

    def get_progress_percentage(self) -> float:
        """计算当前进度百分比"""
        if self.total_frames <= 0:
            return 0.0
        return (self.frame_counter / self.total_frames) * 100

    def get_elapsed_time(self) -> float:
        """获取已用时间（秒）"""
        return time.time() - self.start_time

    def get_estimated_remaining_time(self) -> float:
        """计算预计剩余时间（秒）"""
        if self.frame_counter <= 0:
            return 0.0
        elapsed = self.get_elapsed_time()
        avg_time_per_frame = elapsed / self.frame_counter
        return avg_time_per_frame * (self.total_frames - self.frame_counter)

    def get_processing_fps(self) -> float:
        """计算当前处理速度（FPS）"""
        elapsed = self.get_elapsed_time()
        if elapsed <= 0:
            return 0.0
        return self.frame_counter / elapsed

    def should_report_progress(self) -> bool:
        """判断是否应该报告进度
        
        基于总帧数动态计算报告间隔：总帧数除以100的整数
        例如：1000帧视频每10帧报告一次，500帧视频每5帧报告一次
        """
        if self.total_frames <= 0:
            return False
        
        # 计算动态间隔：总帧数除以100，最小为1
        dynamic_interval = max(1, self.total_frames // 100)
        
        return (self.frame_counter % dynamic_interval == 0 or
                self.frame_counter == self.total_frames - 1)

    def should_update_mq_progress(self) -> bool:
        """判断是否应该更新MQ进度
        
        基于总帧数动态计算MQ更新间隔：总帧数除以200的整数
        例如：1000帧视频每10帧更新一次，500帧视频每5帧更新一次
        """
        if self.total_frames <= 0:
            return False
            
        # 计算动态间隔：总帧数除以100，最小为1
        dynamic_interval = max(1, self.total_frames // 200)
        
        return (self.frame_counter == self.total_frames - 1 or
                self.frame_counter % dynamic_interval == 0)

    def update_ocr_data(self, extracted_info: Optional[Dict[str, Any]]):
        """更新OCR数据"""
        if extracted_info:
            self.recog_date = extracted_info.get("UTC", self.recog_date)
            self.recog_lat = extracted_info.get("Lat", self.recog_lat)
            self.recog_lon = extracted_info.get("Lon", self.recog_lon)

            # 记录首次成功的OCR数据
            if not self.first_ocr_data and any(extracted_info.values()):
                self.first_ocr_data = extracted_info
                self.first_ocr_attempt_success = True

    def get_video_info_dict(self) -> Dict[str, Any]:
        """获取视频信息字典"""
        return {
            'width': self.frame_width,
            'height': self.frame_height,
            'fps': float(self.fps),
            'total_frames': self.total_frames,
            'duration_seconds': float(self.duration_seconds)
        }

    def increment_frame(self):
        """递增帧计数器"""
        self.frame_counter += 1

    def is_first_frame(self) -> bool:
        """判断是否为第一帧"""
        return self.frame_counter == 0

    def is_last_frame(self) -> bool:
        """判断是否为最后一帧"""
        return self.frame_counter == self.total_frames - 1

    def get_video_output_path(self) -> Optional[str]:
        """获取视频输出路径（同步处理模式下通常为None）"""
        # 同步处理模式下，视频输出路径由调用方管理
        return None

    def get_processing_summary(self) -> Dict[str, Any]:
        """获取处理过程摘要"""
        return {
            'total_time': self.get_elapsed_time(),
            'processed_frames': self.frame_counter,
            'total_frames': self.total_frames,
            'processing_fps': self.get_processing_fps(),
            'status': 'completed' if self.frame_counter >= self.total_frames else 'processing'
        }