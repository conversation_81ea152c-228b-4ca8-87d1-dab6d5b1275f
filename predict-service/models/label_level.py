#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
标签层级数据模型 | Label Level Data Model
======================================

标签层级配置的数据结构定义
Data structure definition for label level configuration

主要功能 | Main Features:
- 标签层级定义 | Label level definition
- 颜色配置管理 | Color configuration management
- 告警阈值设置 | Alert threshold setting
- 通知优先级控制 | Notification priority control

作者 | Author: <PERSON><PERSON><PERSON><PERSON>
创建时间 | Created: 2024
最后修改 | Last Modified: 2025
"""

from typing import List, Tuple
from dataclasses import dataclass

@dataclass
class LabelLevel:
    """标签层级配置"""
    name: str
    labels: List[str]
    color: Tuple[int, int, int] # RGB颜色值
    alert_threshold: float
    notification_priority: str