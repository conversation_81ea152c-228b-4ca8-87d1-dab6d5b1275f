#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
视频编码器测试脚本 | Video Codec Test Script
==========================================

用于诊断当前环境支持的视频编码器
Diagnoses video codecs supported in the current environment

主要功能 | Main Features:
- 编码器兼容性测试 | Codec compatibility testing
- 原始视频测试 | Raw video testing
- OpenCV构建信息检查 | OpenCV build information checking
- 编码器性能评估 | Codec performance evaluation

作者 | Author: <PERSON><PERSON><PERSON><PERSON>
创建时间 | Created: 2024
最后修改 | Last Modified: 2025
"""

import cv2
import numpy as np
import os
import tempfile
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_codec(codec_name, container_ext, fps=24.0, size=(640, 480)):
    """测试指定编码器和容器格式组合是否可用"""
    try:
        logger.info(f"Testing codec: {codec_name} with container: {container_ext}")
        fourcc = cv2.VideoWriter_fourcc(*codec_name)

        # 创建临时文件路径
        with tempfile.NamedTemporaryFile(suffix=container_ext, delete=False) as tmp_file:
            output_path = tmp_file.name

        # 创建VideoWriter
        out = cv2.VideoWriter(output_path, fourcc, fps, size)

        if not out.isOpened():
            logger.warning(f"Failed to open VideoWriter with codec: {codec_name} + {container_ext}")
            return False, output_path

        # 创建测试帧
        test_frame = np.random.randint(0, 255, (size[1], size[0], 3), dtype=np.uint8)

        # 尝试写入几帧
        for i in range(5):
            success = out.write(test_frame)
            if not success:
                logger.warning(f"Failed to write frame {i} with codec: {codec_name} + {container_ext}")
                out.release()
                return False, output_path

        # 释放资源
        out.release()

        # 检查文件是否创建并有内容
        if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            logger.info(f" Codec {codec_name} + {container_ext} works! File size: {os.path.getsize(output_path)} bytes")
            return True, output_path
        else:
            logger.warning(f" Codec {codec_name} + {container_ext} failed: no output file or empty file")
            return False, output_path

    except Exception as e:
        logger.error(f" Codec {codec_name} + {container_ext} failed with exception: {str(e)}")
        return False, output_path
    finally:
        # 清理测试文件
        if 'output_path' in locals() and os.path.exists(output_path):
            try:
                os.remove(output_path)
            except:
                pass

def test_raw_video():
    """测试原始未压缩视频输出"""
    try:
        logger.info("Testing raw uncompressed video output...")
        with tempfile.NamedTemporaryFile(suffix='.avi', delete=False) as tmp_file:
            output_path = tmp_file.name

        # 使用0作为fourcc表示未压缩
        out = cv2.VideoWriter(output_path, 0, 24.0, (640, 480))

        if not out.isOpened():
            logger.warning("Failed to open VideoWriter for raw video")
            return False

        # 创建测试帧
        test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)

        # 写入几帧
        for i in range(3):
            success = out.write(test_frame)
            if not success:
                logger.warning(f"Failed to write raw frame {i}")
                out.release()
                return False

        out.release()

        # 检查文件
        if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            logger.info(f" Raw video works! File size: {os.path.getsize(output_path)} bytes")
            os.remove(output_path)
            return True
        else:
            logger.warning(" Raw video failed: no output file or empty file")
            return False

    except Exception as e:
        logger.error(f" Raw video failed with exception: {str(e)}")
        return False
    finally:
        if 'output_path' in locals() and os.path.exists(output_path):
            try:
                os.remove(output_path)
            except:
                pass

def test_all_codecs():
    """测试所有常见的视频编码器和容器格式组合"""
    # 定义codec和对应的最佳容器格式
    codec_container_combinations = [
        # 最基础的组合优先
        ('MJPG', '.avi'), # MJPEG + AVI - 最兼容
        ('mp4v', '.avi'), # MPEG-4 + AVI - 高兼容性
        ('XVID', '.avi'), # XVID + AVI - 传统但稳定
        ('mp4v', '.mp4'), # MPEG-4 + MP4 - 现代格式
        ('DIVX', '.avi'), # DivX + AVI
        ('FMP4', '.avi'), # FFmpeg MPEG-4 + AVI
        ('FMP4', '.mp4'), # FFmpeg MPEG-4 + MP4

        # H.264 相关（需要编码器支持）
        ('h264', '.mp4'), # H.264 + MP4
        ('H264', '.mp4'), # H.264 + MP4
        ('X264', '.mp4'), # x264 + MP4
        ('avc1', '.mp4'), # AVC1 + MP4

        # MPEG 系列 - 适合特定格式
        ('MPG1', '.mpg'),
        ('MPG2', '.mpg'),
        ('MPG1', '.avi'),
        ('MPG2', '.avi'),

        # 备用选项
        ('MJPG', '.mp4'), # MJPEG + MP4（有时可行）
        ('XVID', '.mp4'), # 测试XVID在MP4中的兼容性
    ]

    working_combinations = []
    failed_combinations = []

    # 首先测试原始视频
    logger.info("="*50)
    if test_raw_video():
        working_combinations.append(('RAW', '.avi'))
    else:
        failed_combinations.append(('RAW', '.avi'))

    logger.info("="*50)
    for codec, container in codec_container_combinations:
        success, _ = test_codec(codec, container)
        if success:
            working_combinations.append((codec, container))
        else:
            failed_combinations.append((codec, container))

    logger.info("\n" + "="*50)
    logger.info("CODEC TEST RESULTS")
    logger.info("="*50)

    if working_combinations:
        logger.info(" Working codec + container combinations:")
        for i, (codec, container) in enumerate(working_combinations, 1):
            logger.info(f" {i}. {codec} + {container}")
    else:
        logger.info(" Working codec + container combinations: []")

    if failed_combinations:
        logger.info(" Failed codec + container combinations:")
        for codec, container in failed_combinations:
            logger.info(f" - {codec} + {container}")

    if working_combinations:
        logger.info(f"\n Recommended codec + container order for fallback:")
        for i, (codec, container) in enumerate(working_combinations, 1):
            logger.info(f" {i}. {codec} with {container}")
    else:
        logger.error(" No working codec combinations found! Please check OpenCV and FFmpeg installation.")

    # 返回仅codec名称列表以保持向后兼容
    working_codecs = [combo[0] for combo in working_combinations]
    failed_codecs = list(set([combo[0] for combo in failed_combinations]))

    return working_codecs, failed_codecs

if __name__ == "__main__":
    logger.info("Starting video codec compatibility test...")
    logger.info(f"OpenCV version: {cv2.__version__}")

    # 显示OpenCV构建信息
    build_info = cv2.getBuildInformation()
    logger.info("\nOpenCV Build Information (relevant parts):")
    for line in build_info.split('\n'):
        if any(keyword in line.lower() for keyword in ['ffmpeg', 'codec', 'video']):
            logger.info(f" {line}")

    logger.info("\n" + "="*50)
    working, failed = test_all_codecs()