#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
工具包 | Utils Package
=====================

视频处理和实用工具包
Video processing and utility tools package

包含模块 | Included Modules:
- 视频处理工具 | Video processing utilities
- CUDA内存管理 | CUDA memory management
- 编码器优化 | Codec optimization
- 帧处理函数 | Frame processing functions

作者 | Author: <PERSON><PERSON><PERSON><PERSON>
创建时间 | Created: 2024
最后修改 | Last Modified: 2025
"""

from .video_utils import annotate_frame, compute_confidence_map, apply_heatmap
from .cuda_utils import safe_cuda_empty_cache, safe_cuda_reset, get_cuda_memory_info

__all__ = [
    'annotate_frame',
    'compute_confidence_map',
    'apply_heatmap',
    'safe_cuda_empty_cache',
    'safe_cuda_reset',
    'get_cuda_memory_info'
]