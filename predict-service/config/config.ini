[general]
output_dir = /app/output
max_workers = 4
gas_leakage_mode = true

[model]
model_path = ./models/best-mini.pt
enable_cuda = true

[smart_ocr]
# 智能OCR策略配置
early_detection_frames = 30
early_detection_interval = 10
success_interval = 10
enable_failure_retry = false
failure_retry_interval = 100
enable_optimization = true
log_detailed_stats = true
estimated_ocr_time_per_frame = 0.1

[rabbitmq]
host=rabbitmq
port=5672
username=deploy
password=deploy@1234
virtual_host=transfer

[minio]
minio_api_url=minio:9000
access_key=minioadmin
secret_key=minioadmin
secure=False
bucket_name=linked-all

[video]
video_codec=h264

[gas_tracking_finalizer]
# 气体追踪最终处理器配置

# 置信度阈值 - 过滤低置信度检测
confidence_threshold = 0.1

# 稳定性阈值 - 过滤不稳定的追踪对象
stability_threshold = 0.5

# 最小追踪时长（帧数）- 过滤生命周期过短的追踪对象
min_track_duration = 3

# 空间合并阈值（像素）- 合并相近的追踪对象
spatial_merge_threshold = 40.0

[quality_control]
# 质量控制配置
min_valid_objects_ratio = 0.05
max_filter_ratio = 0.95
enable_adaptive_thresholds = true