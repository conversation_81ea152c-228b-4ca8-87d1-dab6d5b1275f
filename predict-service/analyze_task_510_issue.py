#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务510气体追踪数据为空问题深度分析

基于诊断结果，模型配置和初始化都是正常的，
问题可能在于：
1. 实际视频中没有检测到气体泄漏
2. 置信度阈值设置过高
3. 帧处理逻辑中的条件判断
"""

import sys
import os
import logging
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.managers.config_manager import ConfigManager
from core.processors.video_processor import VideoProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def analyze_gas_tracking_logic():
    """分析气体追踪逻辑"""
    print("\n=== 分析气体追踪逻辑 ===")
    
    try:
        config = ConfigManager()
        
        # 获取配置参数
        gas_model_path = config.get_config_value('gas_tracking', 'model_path', './models/best-mini.pt')
        gas_confidence = config.get_config_value('gas_tracking', 'confidence_threshold', 0.5)
        gas_enabled = config.get_config_value('gas_tracking', 'enabled', True)
        
        print(f"✓ 当前配置:")
        print(f"  - 模型路径: {gas_model_path}")
        print(f"  - 置信度阈值: {gas_confidence}")
        print(f"  - 气体追踪启用: {gas_enabled}")
        
        # 检查模型文件
        if os.path.exists(gas_model_path):
            file_size = os.path.getsize(gas_model_path) / (1024 * 1024)  # MB
            print(f"✓ 模型文件存在: {gas_model_path} ({file_size:.1f} MB)")
        else:
            print(f"✗ 模型文件不存在: {gas_model_path}")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ 配置分析失败: {e}")
        return False

def simulate_frame_processing_without_detections():
    """模拟没有检测结果的帧处理"""
    print("\n=== 模拟无检测结果的帧处理 ===")
    
    try:
        config = ConfigManager()
        video_processor = VideoProcessor(
            config=config,
            ocr_processor=None,
            gas_leakage_mode=True
        )
        
        print(f"✓ VideoProcessor创建成功")
        
        # 模拟process_frame方法的关键逻辑
        # 当没有检测结果时会发生什么
        
        import numpy as np
        import cv2
        
        # 创建一个纯色测试帧（没有明显特征）
        test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        test_frame[:] = (50, 50, 50)  # 深灰色背景，模拟无特征场景
        
        print(f"✓ 创建无特征测试帧: {test_frame.shape}")
        
        # 模拟_safe_model_inference的返回结果（无检测）
        # 这是关键：当模型没有检测到任何目标时
        mock_empty_results = []  # 空的检测结果
        mock_camera_motion = None
        
        print(f"✓ 模拟空检测结果: {len(mock_empty_results)} 个检测")
        
        # 模拟_process_tracking_results的处理
        if mock_empty_results and len(mock_empty_results) > 0:
            print(f"✓ 有检测结果，会生成tracking_result")
        else:
            print(f"✗ 无检测结果，会创建空的tracking_result")
            tracking_result = {
                'detections': [],
                'tracked_objects': [],
                'frame_analysis': {
                    'total_detections': 0,
                    'confidence_stats': {'mean': 0, 'max': 0, 'min': 0}
                }
            }
            print(f"✓ 空tracking_result: {tracking_result}")
        
        # 关键问题：当tracking_result为空时，gas_tracking_data会是什么？
        gas_tracking_data = {
            'frame_number': 100,
            'timestamp': 3.33,
            'detections': tracking_result['detections'],  # 空列表
            'tracked_objects': tracking_result['tracked_objects'],  # 空列表
            'tracking_statistics': {},  # 可能为空
            'leakage_summary': {},  # 可能为空
            'dispersion_analysis': {},  # 可能为空
            'processing_time': 0.1,
            'total_objects_tracked': 0
        }
        
        print(f"✓ 生成的gas_tracking_data:")
        print(f"  - detections: {len(gas_tracking_data['detections'])}")
        print(f"  - tracked_objects: {len(gas_tracking_data['tracked_objects'])}")
        print(f"  - total_objects_tracked: {gas_tracking_data['total_objects_tracked']}")
        
        # 这就是关键：即使没有检测到气体，也会生成gas_tracking_data
        # 但是所有的检测和追踪列表都是空的
        
        # 模拟frame_result的生成
        frame_result = {
            'gas_mode': True,
            'gas_tracking_results': gas_tracking_data  # 这里不是None，而是包含空数据的字典
        }
        
        print(f"\n✓ frame_result分析:")
        print(f"  - gas_mode: {frame_result['gas_mode']}")
        print(f"  - gas_tracking_results存在: {frame_result.get('gas_tracking_results') is not None}")
        print(f"  - gas_tracking_results类型: {type(frame_result.get('gas_tracking_results'))}")
        
        # 模拟video_service.py中的处理逻辑
        if frame_result.get('gas_mode') and frame_result.get('gas_tracking_results'):
            print(f"✓ 条件满足：gas_mode=True 且 gas_tracking_results存在")
            gas_result = frame_result['gas_tracking_results']
            
            # 检查gasLeakageTrackingData的生成
            if gas_result:
                gas_leakage_tracking_data = json.dumps(gas_result)
                print(f"✓ gasLeakageTrackingData会被生成")
                print(f"  - 数据长度: {len(gas_leakage_tracking_data)} 字符")
                print(f"  - 数据内容: {gas_leakage_tracking_data}")
            else:
                print(f"✗ gas_result为空，gasLeakageTrackingData为None")
        else:
            print(f"✗ 条件不满足，gasLeakageTrackingData为None")
            
        return True
        
    except Exception as e:
        print(f"✗ 模拟处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_user_data():
    """分析用户提供的实际数据"""
    print("\n=== 分析用户实际数据 ===")
    
    print("用户数据显示:")
    print("- gasLeakageTrackingData=null")
    print("- gasAnalysisSummary有数据但totalConfirmedLeaks=0")
    print("- finalTrackedLeakagePoints=[]")
    print("- gasTrackingSummary={}")
    
    print("\n分析结论:")
    print("1. gasLeakageTrackingData=null 说明 frame_result.get('gas_tracking_results') 返回了 None")
    print("2. 这意味着在process_frame方法中，gas_tracking_results没有被正确设置")
    print("3. 可能的原因:")
    print("   a) 模型推理失败，_safe_model_inference返回空结果")
    print("   b) process_frame方法中的异常处理导致gas_tracking_results未设置")
    print("   c) 气体模式未正确启用")
    print("   d) 帧处理过程中发生异常")
    
    print("\n建议排查:")
    print("1. 检查任务510的详细处理日志")
    print("2. 确认gas_leakage_mode是否正确启用")
    print("3. 检查模型推理是否成功")
    print("4. 验证process_frame方法的异常处理")
    
def check_video_processor_process_frame_logic():
    """检查VideoProcessor.process_frame的逻辑"""
    print("\n=== 检查process_frame逻辑 ===")
    
    print("关键代码路径分析:")
    print("1. process_frame方法 (video_processor.py:1925)")
    print("2. 执行标准模式处理")
    print("3. 如果gas_leakage_mode=True，执行气体追踪")
    print("4. _safe_model_inference 进行模型推理")
    print("5. _process_tracking_results 处理追踪结果")
    print("6. 生成gas_tracking_data")
    print("7. gas_tracking_results.append(gas_tracking_data)")
    print("8. 返回integrated_result，包含gas_tracking_results字段")
    
    print("\n可能的失败点:")
    print("1. _safe_model_inference 异常")
    print("2. _process_tracking_results 异常")
    print("3. 气体模式条件判断失败")
    print("4. 异常处理中未设置gas_tracking_results")
    
    print("\n需要检查的日志关键词:")
    print("- '气体泄漏追踪模型初始化'")
    print("- '模型推理'")
    print("- '追踪结果处理'")
    print("- '气体追踪结果保存'")
    print("- 任何异常或错误信息")

def main():
    """主函数"""
    print("任务510气体追踪数据为空问题深度分析")
    print("=" * 60)
    
    # 执行分析
    analyses = [
        ("气体追踪逻辑分析", analyze_gas_tracking_logic),
        ("无检测结果帧处理模拟", simulate_frame_processing_without_detections),
        ("用户数据分析", analyze_user_data),
        ("process_frame逻辑检查", check_video_processor_process_frame_logic)
    ]
    
    for analysis_name, analysis_func in analyses:
        try:
            print(f"\n{'='*20} {analysis_name} {'='*20}")
            analysis_func()
        except Exception as e:
            print(f"\n✗ {analysis_name}分析异常: {e}")
    
    print("\n" + "=" * 60)
    print("🔍 深度分析完成！")
    print("\n💡 关键发现:")
    print("1. 模型配置正确，文件存在")
    print("2. gasLeakageTrackingData=null 表明 gas_tracking_results 未被设置")
    print("3. 需要检查任务510的详细处理日志")
    print("4. 重点关注 process_frame 方法的执行流程")
    
    print("\n🎯 下一步行动:")
    print("1. 查看任务510的完整处理日志")
    print("2. 确认气体模式是否正确启用")
    print("3. 检查模型推理过程是否有异常")
    print("4. 验证帧处理的异常处理逻辑")

if __name__ == "__main__":
    main()