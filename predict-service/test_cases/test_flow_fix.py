#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试光流修复

测试修复后的光流处理是否能正常工作，不再出现索引错误

Author: Trae AI Assistant
Date: 2024
"""

import cv2
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.processors.frame_processor import FrameProcessor


def test_flow_processing():
    """
    测试光流处理修复
    """
    print("测试光流处理修复...")
    
    # 创建测试数据
    h, w = 480, 640
    
    # 创建两个测试帧
    prev_frame = np.random.randint(50, 200, (h, w), dtype=np.uint8)
    curr_frame = prev_frame.copy()
    # 添加一些运动
    curr_frame[100:200, 100:200] = np.roll(prev_frame[100:200, 100:200], 5, axis=1)
    
    # 创建测试mask（多边形格式）
    mask = [[150, 120], [350, 120], [350, 280], [150, 280]]
    
    # 测试参数
    fps = 30.0
    scale = 0.1  # 米/像素
    
    try:
        # 创建处理器
        processor = FrameProcessor()
        
        # 测试光流计算
        print("调用 _compute_flow 方法...")
        result = processor._compute_flow(prev_frame, curr_frame, mask, fps, scale)
        
        print(f"返回结果类型: {type(result)}")
        print(f"返回结果键: {result.keys() if isinstance(result, dict) else 'Not a dict'}")
        
        if isinstance(result, dict):
            if 'image' in result:
                print(f"图像形状: {result['image'].shape}")
                print(f"速度值: {result['velocity']}")
                
                # 保存结果图像
                cv2.imwrite("test_flow_result.jpg", result['image'])
                print("光流结果已保存到: test_flow_result.jpg")
                
                print("✅ 光流处理修复成功！")
                return True
            else:
                print("❌ 返回结果缺少 'image' 字段")
                return False
        else:
            print("❌ 返回结果不是字典格式")
            return False
            
    except Exception as e:
        print(f"❌ 光流处理仍然失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_edge_cases():
    """
    测试边界情况
    """
    print("\n测试边界情况...")
    
    processor = FrameProcessor()
    
    # 测试1: 空mask
    try:
        h, w = 100, 100
        prev_frame = np.zeros((h, w), dtype=np.uint8)
        curr_frame = np.zeros((h, w), dtype=np.uint8)
        empty_mask = []
        
        result = processor._compute_flow(prev_frame, curr_frame, empty_mask, 30.0, 0.1)
        print("✅ 空mask测试通过")
    except Exception as e:
        print(f"❌ 空mask测试失败: {e}")
    
    # 测试2: 单点mask
    try:
        single_point_mask = [[50, 50]]
        result = processor._compute_flow(prev_frame, curr_frame, single_point_mask, 30.0, 0.1)
        print("✅ 单点mask测试通过")
    except Exception as e:
        print(f"❌ 单点mask测试失败: {e}")
    
    # 测试3: 超出边界的mask
    try:
        out_of_bounds_mask = [[-10, -10], [200, -10], [200, 200], [-10, 200]]
        result = processor._compute_flow(prev_frame, curr_frame, out_of_bounds_mask, 30.0, 0.1)
        print("✅ 超出边界mask测试通过")
    except Exception as e:
        print(f"❌ 超出边界mask测试失败: {e}")


if __name__ == "__main__":
    print("开始测试光流修复...")
    
    # 主要测试
    success = test_flow_processing()
    
    # 边界情况测试
    test_edge_cases()
    
    if success:
        print("\n🎉 所有测试通过！光流处理已修复。")
        print("现在四宫格显示应该正常工作了。")
    else:
        print("\n❌ 测试失败，需要进一步调试。")
