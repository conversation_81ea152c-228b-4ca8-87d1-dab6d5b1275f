#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
气体追踪数据处理修复验证脚本

功能：
1. 测试GasTrackingFinalizer的配置文件读取
2. 验证数据过滤逻辑的改进
3. 检查诊断信息的完整性
4. 模拟不同数据质量场景的处理

作者：Trae 编程助手
日期：2024
"""

import sys
import os
import configparser
import logging
from typing import Dict, List, Any
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_config():
    """加载配置文件"""
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(__file__), 'config', 'config.ini')
    
    if os.path.exists(config_path):
        config.read(config_path, encoding='utf-8')
        logger.info(f"配置文件加载成功: {config_path}")
    else:
        logger.warning(f"配置文件不存在: {config_path}，使用默认配置")
    
    return config

def create_test_tracking_data(scenario: str) -> List[Dict[str, Any]]:
    """
    创建测试用的追踪数据
    
    Args:
        scenario: 测试场景 ('empty', 'low_confidence', 'short_duration', 'normal')
    
    Returns:
        模拟的追踪数据列表
    """
    if scenario == 'empty':
        return []
    
    elif scenario == 'low_confidence':
        # 所有数据都是低置信度
        return [
            {
                'frame_number': i,
                'detections': [
                    {
                        'track_id': 1,
                        'confidence': 0.05,  # 低于阈值0.1
                        'bbox': [100, 100, 50, 50],
                        'area': 2500
                    }
                ]
            }
            for i in range(10)
        ]
    
    elif scenario == 'short_duration':
        # 追踪时长过短
        return [
            {
                'frame_number': i,
                'detections': [
                    {
                        'track_id': 1,
                        'confidence': 0.8,  # 高置信度
                        'bbox': [100, 100, 50, 50],
                        'area': 2500
                    }
                ]
            }
            for i in range(2)  # 只有2帧，低于阈值3
        ]
    
    elif scenario == 'normal':
        # 正常数据
        return [
            {
                'frame_number': i,
                'detections': [
                    {
                        'track_id': 1,
                        'confidence': 0.8,
                        'bbox': [100 + i, 100 + i, 50, 50],
                        'area': 2500
                    },
                    {
                        'track_id': 2,
                        'confidence': 0.7,
                        'bbox': [200 + i, 200 + i, 40, 40],
                        'area': 1600
                    }
                ]
            }
            for i in range(15)  # 15帧数据
        ]
    
    else:
        raise ValueError(f"未知的测试场景: {scenario}")

def test_gas_tracking_finalizer():
    """测试GasTrackingFinalizer的功能"""
    try:
        # 导入必要的模块
        from core.processors.gas_tracking_finalizer import GasTrackingFinalizer
        
        # 加载配置
        config = load_config()
        
        # 从配置文件读取参数
        confidence_threshold = float(config.get('gas_tracking_finalizer', 'confidence_threshold', fallback=0.1))
        stability_threshold = float(config.get('gas_tracking_finalizer', 'stability_threshold', fallback=0.5))
        min_track_duration = int(config.get('gas_tracking_finalizer', 'min_track_duration', fallback=3))
        spatial_merge_threshold = float(config.get('gas_tracking_finalizer', 'spatial_merge_threshold', fallback=40.0))
        
        logger.info(f"配置参数: confidence_threshold={confidence_threshold}, "
                   f"stability_threshold={stability_threshold}, "
                   f"min_track_duration={min_track_duration}, "
                   f"spatial_merge_threshold={spatial_merge_threshold}")
        
        # 初始化处理器
        finalizer = GasTrackingFinalizer(
            confidence_threshold=confidence_threshold,
            stability_threshold=stability_threshold,
            min_track_duration=min_track_duration,
            spatial_merge_threshold=spatial_merge_threshold
        )
        
        # 测试不同场景
        test_scenarios = ['empty', 'low_confidence', 'short_duration', 'normal']
        
        for scenario in test_scenarios:
            logger.info(f"\n{'='*50}")
            logger.info(f"测试场景: {scenario}")
            logger.info(f"{'='*50}")
            
            # 创建测试数据
            test_data = create_test_tracking_data(scenario)
            logger.info(f"输入数据帧数: {len(test_data)}")
            
            if test_data:
                total_detections = sum(len(frame.get('detections', [])) for frame in test_data)
                logger.info(f"总检测对象数: {total_detections}")
            
            # 处理数据
            try:
                result = finalizer.process_final_gas_tracking_data(test_data)
                
                # 输出结果
                status = result.processing_info.get('status', 'unknown') if result.processing_info else 'unknown'
                error_msg = result.processing_info.get('error_message', 'None') if result.processing_info else 'None'
                logger.info(f"处理状态: {status}")
                logger.info(f"错误信息: {error_msg}")
                
                if result.processing_info:
                    logger.info("处理信息:")
                    for key, value in result.processing_info.items():
                        logger.info(f"  {key}: {value}")
                
                if result.overall_statistics:
                    logger.info("统计信息:")
                    stats = result.overall_statistics
                    logger.info(f"  总帧数: {stats.get('total_frames', 0)}")
                    logger.info(f"  有效帧数: {stats.get('valid_frames', 0)}")
                    logger.info(f"  总检测数: {stats.get('total_detections', 0)}")
                    logger.info(f"  有效检测数: {stats.get('valid_detections', 0)}")
                    logger.info(f"  最终泄漏点数: {len(result.final_leakage_points)}")
                
                if result.risk_assessment:
                    logger.info(f"风险等级: {result.risk_assessment.get('risk_level', 'unknown')}")
                    logger.info(f"风险分数: {result.risk_assessment.get('risk_score', 0)}")
                
            except Exception as e:
                logger.error(f"处理失败: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
        
        logger.info(f"\n{'='*50}")
        logger.info("测试完成")
        logger.info(f"{'='*50}")
        
    except ImportError as e:
        logger.error(f"导入模块失败: {str(e)}")
        logger.error("请确保在正确的Python环境中运行此脚本")
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

def test_video_service_config():
    """测试VideoService的配置读取"""
    try:
        # 模拟VideoService的配置读取逻辑
        config = load_config()
        
        logger.info("\n测试VideoService配置读取:")
        
        # 读取gas_tracking_finalizer配置
        confidence_threshold = float(config.get('gas_tracking_finalizer', 'confidence_threshold', fallback=0.1))
        stability_threshold = float(config.get('gas_tracking_finalizer', 'stability_threshold', fallback=0.5))
        min_track_duration = int(config.get('gas_tracking_finalizer', 'min_track_duration', fallback=3))
        spatial_merge_threshold = float(config.get('gas_tracking_finalizer', 'spatial_merge_threshold', fallback=40.0))
        
        logger.info(f"VideoService将使用以下参数:")
        logger.info(f"  confidence_threshold: {confidence_threshold}")
        logger.info(f"  stability_threshold: {stability_threshold}")
        logger.info(f"  min_track_duration: {min_track_duration}")
        logger.info(f"  spatial_merge_threshold: {spatial_merge_threshold}")
        
        # 读取质量控制配置
        min_valid_objects_ratio = float(config.get('quality_control', 'min_valid_objects_ratio', fallback=0.05))
        max_filter_ratio = float(config.get('quality_control', 'max_filter_ratio', fallback=0.95))
        enable_adaptive_thresholds = config.getboolean('quality_control', 'enable_adaptive_thresholds', fallback=True)
        
        logger.info(f"质量控制参数:")
        logger.info(f"  min_valid_objects_ratio: {min_valid_objects_ratio}")
        logger.info(f"  max_filter_ratio: {max_filter_ratio}")
        logger.info(f"  enable_adaptive_thresholds: {enable_adaptive_thresholds}")
        
    except Exception as e:
        logger.error(f"配置读取测试失败: {str(e)}")

if __name__ == '__main__':
    logger.info("开始气体追踪数据处理修复验证")
    
    # 测试配置读取
    test_video_service_config()
    
    # 测试GasTrackingFinalizer
    test_gas_tracking_finalizer()
    
    logger.info("验证完成")