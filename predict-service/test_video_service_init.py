#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试VideoService初始化，特别是GasTrackingFinalizer的配置读取
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_config_manager():
    """测试ConfigManager的get_config_value方法"""
    try:
        from core.managers.config_manager import ConfigManager
        
        logger.info("=== 测试ConfigManager ===")
        config = ConfigManager()
        logger.info("ConfigManager初始化成功")
        
        # 测试get_config_value方法
        confidence_threshold = config.get_config_value('gas_tracking_finalizer', 'confidence_threshold', fallback=0.1)
        stability_threshold = config.get_config_value('gas_tracking_finalizer', 'stability_threshold', fallback=0.5)
        min_track_duration = config.get_config_value('gas_tracking_finalizer', 'min_track_duration', fallback=3)
        spatial_merge_threshold = config.get_config_value('gas_tracking_finalizer', 'spatial_merge_threshold', fallback=40.0)
        
        logger.info(f"配置读取成功:")
        logger.info(f"  confidence_threshold: {confidence_threshold}")
        logger.info(f"  stability_threshold: {stability_threshold}")
        logger.info(f"  min_track_duration: {min_track_duration}")
        logger.info(f"  spatial_merge_threshold: {spatial_merge_threshold}")
        
        return True
        
    except Exception as e:
        logger.error(f"ConfigManager测试失败: {e}")
        return False

def test_gas_tracking_finalizer_init():
    """测试GasTrackingFinalizer初始化"""
    try:
        from core.processors.gas_tracking_finalizer import GasTrackingFinalizer
        from core.managers.config_manager import ConfigManager
        
        logger.info("=== 测试GasTrackingFinalizer初始化 ===")
        
        config = ConfigManager()
        
        # 模拟VideoService中的_init_gas_tracking_finalizer方法
        confidence_threshold = float(config.get_config_value('gas_tracking_finalizer', 'confidence_threshold', fallback=0.1))
        stability_threshold = float(config.get_config_value('gas_tracking_finalizer', 'stability_threshold', fallback=0.5))
        min_track_duration = int(config.get_config_value('gas_tracking_finalizer', 'min_track_duration', fallback=3))
        spatial_merge_threshold = float(config.get_config_value('gas_tracking_finalizer', 'spatial_merge_threshold', fallback=40.0))
        
        logger.info(f"使用参数初始化GasTrackingFinalizer:")
        logger.info(f"  confidence_threshold: {confidence_threshold}")
        logger.info(f"  stability_threshold: {stability_threshold}")
        logger.info(f"  min_track_duration: {min_track_duration}")
        logger.info(f"  spatial_merge_threshold: {spatial_merge_threshold}")
        
        finalizer = GasTrackingFinalizer(
            confidence_threshold=confidence_threshold,
            stability_threshold=stability_threshold,
            min_track_duration=min_track_duration,
            spatial_merge_threshold=spatial_merge_threshold
        )
        
        logger.info("GasTrackingFinalizer初始化成功")
        return True
        
    except Exception as e:
        logger.error(f"GasTrackingFinalizer初始化失败: {e}")
        logger.exception("详细错误信息:")
        return False

def test_video_service_init_method():
    """测试VideoService的_init_gas_tracking_finalizer方法"""
    try:
        logger.info("=== 测试VideoService._init_gas_tracking_finalizer方法 ===")
        
        # 创建一个模拟的VideoService实例来测试_init_gas_tracking_finalizer方法
        from core.managers.config_manager import ConfigManager
        
        class MockVideoService:
            def __init__(self):
                self.config = ConfigManager()
            
            def _init_gas_tracking_finalizer(self):
                """从配置文件初始化气体追踪最终处理器"""
                from core.processors.gas_tracking_finalizer import GasTrackingFinalizer
                
                # 从配置文件读取参数，如果没有配置则使用默认值
                confidence_threshold = float(self.config.get_config_value('gas_tracking_finalizer', 'confidence_threshold', fallback=0.1))
                stability_threshold = float(self.config.get_config_value('gas_tracking_finalizer', 'stability_threshold', fallback=0.5))
                min_track_duration = int(self.config.get_config_value('gas_tracking_finalizer', 'min_track_duration', fallback=3))
                spatial_merge_threshold = float(self.config.get_config_value('gas_tracking_finalizer', 'spatial_merge_threshold', fallback=40.0))
                
                logger.info(f"初始化GasTrackingFinalizer参数: confidence_threshold={confidence_threshold}, "
                           f"stability_threshold={stability_threshold}, min_track_duration={min_track_duration}, "
                           f"spatial_merge_threshold={spatial_merge_threshold}")
                
                return GasTrackingFinalizer(
                    confidence_threshold=confidence_threshold,
                    stability_threshold=stability_threshold,
                    min_track_duration=min_track_duration,
                    spatial_merge_threshold=spatial_merge_threshold
                )
        
        mock_service = MockVideoService()
        finalizer = mock_service._init_gas_tracking_finalizer()
        
        logger.info("VideoService._init_gas_tracking_finalizer方法测试成功")
        return True
        
    except Exception as e:
        logger.error(f"VideoService._init_gas_tracking_finalizer方法测试失败: {e}")
        logger.exception("详细错误信息:")
        return False

def main():
    """主测试函数"""
    logger.info("开始测试VideoService初始化相关功能")
    logger.info("=" * 60)
    
    tests = [
        ("ConfigManager功能", test_config_manager),
        ("GasTrackingFinalizer初始化", test_gas_tracking_finalizer_init),
        ("VideoService._init_gas_tracking_finalizer方法", test_video_service_init_method)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name} - 测试通过")
            else:
                logger.error(f"❌ {test_name} - 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} - 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试总结
    logger.info("\n" + "=" * 60)
    logger.info("测试总结:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        logger.info("🎉 所有测试通过！ConfigManager修复成功！")
    else:
        logger.error("⚠️  部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()